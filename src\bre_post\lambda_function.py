import os
import json

from common.aria_helper import CrudStatuses
from common.aria_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common.aria_helper.boto3_utils import get_secret
from common.aria_helper import ARIA
from common import Mongo

environment = os.environ['ENV']
database_name = os.environ['DATABASE_NAME']
aria_environment = os.environ['ARIA_ENVIRONMENT']


class BrePostHandler:
    def __init__(self, event):
        self.event = event
        self.input_body = json.loads(event['body'])
        self.action_data = self.input_body['action']
        self.document = self.input_body['document']
        self.app_id = self.document['app_id']
        self.statuses = self.input_body['status']
        self.execution_id = self.input_body.get('execution_id')
        self.validation_result = self.input_body.get('validation_result', {})
        self.is_valid = self.input_body.get('is_valid', False)
        self.bre_type = self.input_body.get('bre_type', '')
        
        self.mongo_client = Mongo(get_secret(environment + '-mongodb_uri', return_json=False)).client
        self.aria_secret = get_secret(secret_name=f'{environment}-aria_cm_tokens')
        self.crud_handler = CrudHandler(self.mongo_client)
        self.crud_statuses = CrudStatuses(self.mongo_client)

    def build_bre_response(self):
        """
        Build the BRE response based on validation results and business logic.
        """
        bre_response = {}
        
        # Determine target status based on validation result
        if self.is_valid:
            # Find approved/success status
            target_status_id = [k for k, v in self.statuses.items() 
                              if 'approved' in v['label'].lower() or 'success' in v['label'].lower()]
            aria_exception = None
        else:
            # Find rejected/needs review status
            target_status_id = [k for k, v in self.statuses.items() 
                              if 'rejected' in v['label'].lower() or 'needs' in v['label'].lower()]
            # Build exception message from validation errors
            validation_errors = self.validation_result.get('errors', [])
            aria_exception = f"Validation failed: {'; '.join(validation_errors)}" if validation_errors else "Validation failed"

        if not target_status_id:
            raise ValueError("No appropriate target status found")

        # Build the response
        bre_response["aria_status"] = {"value": target_status_id}
        
        if aria_exception:
            bre_response["aria_exception"] = {"value": aria_exception}
            
        # Add validation details if available
        if self.validation_result:
            bre_response["validation_details"] = {"value": json.dumps(self.validation_result)}

        return bre_response

    def post_to_aria(self, bre_response):
        """
        Post the BRE response to ARIA.
        """
        aria = ARIA(
            base_url=self.aria_secret[aria_environment]['url'],
            request_token=self.aria_secret[aria_environment]['token']
        )
        
        aria.bre_reply(
            app_id=self.document['app_id'],
            item_id=self.document['id'],
            bre_response=bre_response
        )

    def create_aria_event(self, status_message):
        """
        Create an event in ARIA for tracking purposes.
        """
        aria = ARIA(
            base_url=self.aria_secret[aria_environment]['url'],
            request_token=self.aria_secret[aria_environment]['token']
        )
        
        event_status = 0 if self.is_valid else 1  # 0 = Completed, 1 = Failed
        
        aria.create_event(
            app_id=self.document['app_id'],
            item_id=self.document['id'],
            title="Validation and Processing Complete",
            body=status_message,
            status=event_status
        )

    def run(self):
        try:
            # Build BRE response based on validation results
            bre_response = self.build_bre_response()
            
            # Post to ARIA
            self.post_to_aria(bre_response)
            
            # Create tracking event
            status_message = f"Validation {'passed' if self.is_valid else 'failed'}. BRE type: {self.bre_type}"
            self.create_aria_event(status_message)
            
            # Mark execution as completed
            if self.execution_id:
                self.crud_handler.mark_as_completed(self.execution_id)
            
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'BRE response posted successfully',
                    'validation_passed': self.is_valid,
                    'bre_type': self.bre_type
                })
            }

        except Exception as e:
            # Mark execution as failed
            if self.execution_id:
                self.crud_handler.mark_as_failed(self.execution_id, error_message=str(e))
            
            return {
                'statusCode': 500,
                'body': json.dumps({'message': 'Issue while posting BRE response: ' + str(e)})
            }


def lambda_handler(event, context):
    print(event)
    if 'body' not in list(event.keys()):
        raise ValueError("body tag is missing on the dict. Skipping...")

    bre_post_handler = BrePostHandler(event)
    return bre_post_handler.run()
