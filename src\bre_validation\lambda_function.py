import os
import json

from validation_helper.validation_execution import ValidatorExecution
from common.aria_helper import CrudStatuses
from common.aria_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common.aria_helper.boto3_utils import get_secret, trigger_lambda, trigger_lambda_response
from common import Mongo

environment = os.environ['ENV']
database_name = os.environ['DATABASE_NAME']
aria_environment = os.environ['ARIA_ENVIRONMENT']


class BreValidationHandler:
    def __init__(self, event):
        self.event = event
        self.input_body = json.loads(event['body'])
        self.action_data = self.input_body['action']
        self.document = self.input_body['document']
        self.app_id = self.document['app_id']
        self.statuses = self.input_body['status']
        self.mongo_client = Mongo(get_secret(environment + '-mongodb_uri', return_json=False)).client
        self.aria_secret = get_secret(secret_name=f'{environment}-aria_cm_tokens')
        self.crud_handler = CrudHandler(self.mongo_client)
        self.crud_statuses = CrudStatuses(self.mongo_client)
        self.validator_execution = ValidatorExecution()
        self.validation_config = self.mongo_client[database_name]["validation_config"].find_one({"app_id": self.app_id})

    def next_step(self):
        """
        Determines the next function to call after validation.
        For validation, the next step is typically bre_post.
        """
        # Get the action configuration to determine next function
        self.mongo_client.select_db_and_collection(database_name, collection_name="action_config")
        actions_dict = self.mongo_client.find_one({"app_id": self.app_id})

        action_name = self.action_data.get('action_label', '').lower()
        document_type = self.document['ocr_groups'][0] if self.document.get('ocr_groups') else None

        next_function = None
        bre_type = "validation"
        request_response = True  # Validation typically waits for response
        lambda_trigger_type = trigger_lambda_response

        # Look up the next function from action config
        if action_name and document_type and actions_dict and document_type in actions_dict:
            if action_name in actions_dict[document_type]:
                action_vals = actions_dict[document_type][action_name]
                # For validation, we typically want the post function
                next_function = os.environ.get('BRE_POST_FUNCTION', 'bre_post')
                request_response = action_vals.get('request_response', True)
                lambda_trigger_type = trigger_lambda_response if request_response else trigger_lambda
                bre_type = action_vals.get('bre_type', 'validation')

        # Default fallback
        if not next_function:
            next_function = os.environ.get('BRE_POST_FUNCTION', 'bre_post')

        return bre_type, next_function, lambda_trigger_type, request_response

    def run(self):
        try:
            # Perform validation first
            is_valid, validation_result = self.validator_execution.validate_data(self.document, self.validation_config)

            # Determine next step
            bre_type, next_function, method, request_response = self.next_step()

            # Save on db what we selected on this run
            execution_id = self.crud_handler.insert_execution(self.input_body, next_function)

            # Update the information on the input_body
            self.input_body['bre_type'] = bre_type
            self.input_body['execution_id'] = execution_id
            self.input_body['request_response'] = request_response
            self.input_body['validation_result'] = validation_result
            self.input_body['is_valid'] = is_valid

            # Calling the next step
            try:
                lambda_response = method(next_function, self.input_body)
            except Exception as e:
                self.crud_handler.mark_as_failed(execution_id, error_message=str(e))
                return {
                    'statusCode': 500,
                    'body': json.dumps({'message': 'Issue while triggering next lambda: ' + str(e)})
                }

            # If we wait for response, return the response from the next lambda
            if method == trigger_lambda_response:
                response_payload = json.loads(lambda_response['Payload'].read())
                print("RESPONSE FROM LAMBDA ->> ", response_payload)

                return {
                    'statusCode': response_payload.get('statusCode'),
                    'body': response_payload.get('body')
                }

            return {
                'statusCode': 200,
                'body': json.dumps({'message': 'Validation completed, working on next step'})
            }

        except Exception as e:
            self.crud_handler.insert_execution(self.event, 'None', failed=True, error_message=str(e))
            return {
                'statusCode': 500,
                'body': {'message': 'Issue while processing the petition: ' + str(e)}
            }


def lambda_handler(event, context):
    print(event)
    if 'body' not in list(event.keys()):
        raise ValueError("body tag is missing on the dict. Skipping...")

    bre_validation_handler = BreValidationHandler(event)
    return bre_validation_handler.run()